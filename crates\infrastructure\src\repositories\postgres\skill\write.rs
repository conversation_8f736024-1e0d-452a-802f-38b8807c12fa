use async_trait::async_trait;
use num_traits::FromPrimitive;
use reforged_domain::models::skill::value_object::SkillId;
use reforged_domain::{
    error::RepositoryError, models::skill::entity::Skill,
    repository::skill_repository::SkillRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::EntityTrait;
use sea_orm::entity::prelude::Decimal;
use sea_orm::{ActiveModelTrait, ActiveValue::Set, DatabaseConnection};

use crate::SeaORMErr;
use crate::mappers::skill_mapper::SkillDbModelMapper;

use crate::models::skills::ActiveModel as SkillsActiveModel;
use crate::models::skills::Entity as Skills;

#[allow(dead_code)]
pub struct PostgresSkillRepository {
    pool: DatabaseConnection,
}

impl PostgresSkillRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl SkillRepository for PostgresSkillRepository {
    async fn save(&self, skill: &Skill) -> Result<Skill, RepositoryError> {
        let skill_active_model = SkillsActiveModel {
            id: Set(skill.id().get_id()),
            name: Set(skill.name().value()),
            animation: Set(skill.animation().value()),
            description: Set(skill.description().value()),
            damage: Set(Decimal::from_f64(skill.damage().value()).unwrap_or_default()),
            mana: Set(skill.mana().value()),
            mana_back: Set(skill.mana_back().value()),
            life_steal: Set(Decimal::from_f64(skill.life_steal().value()).unwrap_or_default()),
            icon: Set(skill.icon().value()),
            range: Set(skill.range().value()),
            dsrc: Set(skill.dsrc().value()),
            reference: Set(skill.reference().value()),
            target: Set(skill.target().value()),
            effects: Set(skill.effects().value()),
            r#type: Set(skill.skill_type().value()),
            strl: Set(skill.strl().value()),
            cooldown: Set(skill.cooldown().value()),
            hit_targets: Set(skill.hit_targets().value()),
            pet: Set(if skill.pet().value() { 1 } else { 0 }),
            chance: Set(skill
                .chance()
                .value()
                .map(|c| Decimal::from_f64(c).unwrap_or_default())),
            show_damage: Set(skill.show_damage().value()),
        };

        let model = skill_active_model
            .insert(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        let mapped_skill = SkillDbModelMapper::new(model);
        let skill = Skill::from(mapped_skill);

        Ok(skill)
    }

    async fn update(&self, skill: &Skill) -> Result<(), RepositoryError> {
        let skill_active_model = SkillsActiveModel {
            id: Set(skill.id().get_id()),
            name: Set(skill.name().value()),
            animation: Set(skill.animation().value()),
            description: Set(skill.description().value()),
            damage: Set(Decimal::from_f64(skill.damage().value()).unwrap_or_default()),
            mana: Set(skill.mana().value()),
            mana_back: Set(skill.mana_back().value()),
            life_steal: Set(Decimal::from_f64(skill.life_steal().value()).unwrap_or_default()),
            icon: Set(skill.icon().value()),
            range: Set(skill.range().value()),
            dsrc: Set(skill.dsrc().value()),
            reference: Set(skill.reference().value()),
            target: Set(skill.target().value()),
            effects: Set(skill.effects().value()),
            r#type: Set(skill.skill_type().value()),
            strl: Set(skill.strl().value()),
            cooldown: Set(skill.cooldown().value()),
            hit_targets: Set(skill.hit_targets().value()),
            pet: Set(if skill.pet().value() { 1 } else { 0 }),
            chance: Set(skill
                .chance()
                .value()
                .map(|c| Decimal::from_f64(c).unwrap_or_default())),
            show_damage: Set(skill.show_damage().value()),
        };

        let result = skill_active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.id != skill.id().get_id() {
            return Err(RepositoryError::NotFound(format!(
                "Skill with id {} not found for update",
                skill.id()
            )));
        }

        Ok(())
    }

    async fn delete(&self, skill_id: &SkillId) -> Result<(), RepositoryError> {
        let model = SkillsActiveModel {
            id: Set(skill_id.get_id()),
            ..Default::default()
        };

        let result = Skills::delete(model)
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if result.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!(
                "Skill with id {} not found for deletion",
                skill_id
            )));
        }

        Ok(())
    }
}
