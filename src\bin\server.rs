use actix_web::{HttpServer, dev::ServerHandle};
use esrs::{manager::AggregateManager, store::postgres::PgStoreBuilder};
use lapin::{
    ExchangeKind,
    options::{ExchangeDeclareOptions, QueueBindOptions, QueueDeclareOptions},
    types::{AMQPValue, FieldTable, ShortString},
};
use reforged_api::{router::create_router, state::ApiState};
use reforged_application::queries::{
    class_category::handler::ClassCategoryQueryHandler,
    item_rarities::handler::ItemRarityQueryHandler,
    password_reset_token::handler::PasswordResetTokenQueryHandler, user::handler::User<PERSON>ueryHandler,
};
use reforged_application::{
    eventsource::user::UserAggregateEventBus, queries::skills::handler::SkillQueryHandler,
};
use reforged_infrastructure::{
    Config,
    broker::MessageBrokerConfig,
    class_category::{
        read::PostgresClassCategoryReadRepository, write::PostgresClassCategoryRepository,
    },
    connection::create_pool,
    disk_storage::DiskStorageProvider,
    hashing::Argon2Hasher,
    item_rarity::PostgresItemRarityReadRepository,
    loader::load_api_config,
    paseto::PasetoAuthenticationTokenService,
    password_reset_token::{
        read::PostgresPasswordResetTokenReadRepository, write::PostgresPasswordResetTokenRepository,
    },
    rabbit::RabbitMQ,
    skill::{PostgresSkillReadRepository, PostgresSkillRepository},
    token::TokenConfig,
    turnstile::TurnstileCaptcha,
    user::{read::PostgresUserReadRepository, write::PostgresUserRepository},
};
use std::sync::Arc;
use tokio::signal;
use tracing::info;
use tracing_log::LogTracer;
use tracing_subscriber::{Layer, layer::SubscriberExt};

#[actix_web::main]
async fn main() -> anyhow::Result<()> {
    setup_tracing();
    setup_rustls();

    let config = load_api_config()?;
    let eventstore_config = config.event_store();
    let db_config = config.database();
    let http_config = config.http();
    let captcha_config = config.captcha();
    let token_config = TokenConfig::from_env()?;
    let broker_config = MessageBrokerConfig::from_env()?;

    let pool = create_pool(&db_config.get_connection_string()).await?;
    let eventstore_pool = create_pool(&eventstore_config.get_connection_string()).await?;

    let user_repository = PostgresUserRepository::new(pool.clone());
    let user_repository = Arc::new(user_repository);
    let user_read_repository = PostgresUserReadRepository::new(pool.clone());
    let user_read_repository = Arc::new(user_read_repository);
    let class_category_repository = PostgresClassCategoryRepository::new(pool.clone());
    let class_category_repository = Arc::new(class_category_repository);
    let class_category_read_repository = PostgresClassCategoryReadRepository::new(pool.clone());
    let class_category_read_repository = Arc::new(class_category_read_repository);
    let password_reset_token_repository = PostgresPasswordResetTokenRepository::new(pool.clone());
    let password_reset_token_repository = Arc::new(password_reset_token_repository);
    let password_reset_token_read_repository =
        PostgresPasswordResetTokenReadRepository::new(pool.clone());
    let password_reset_token_read_repository = Arc::new(password_reset_token_read_repository);
    let skill_read_repository = PostgresSkillReadRepository::new(pool.clone());
    let skill_read_repository = Arc::new(skill_read_repository);
    let skill_repository = PostgresSkillRepository::new(pool.clone());
    let skill_repository = Arc::new(skill_repository);
    let item_rarity_read_repository = PostgresItemRarityReadRepository::new(pool.clone());
    let item_rarity_read_repository = Arc::new(item_rarity_read_repository);

    let password_hasher = Argon2Hasher::new();
    let password_hasher = Arc::new(password_hasher);

    let connection =
        lapin::Connection::connect(&broker_config.url, lapin::ConnectionProperties::default())
            .await?;
    let channel = connection.create_channel().await?;

    let mut exchange_options = ExchangeDeclareOptions::default();
    exchange_options.durable = true;

    let mut args = FieldTable::default();
    args.insert(ShortString::from("x-message-ttl"), AMQPValue::LongInt(30));

    channel
        .exchange_declare(
            &broker_config.exchange,
            ExchangeKind::Fanout,
            exchange_options,
            args.clone(),
        )
        .await?;

    let mut options = QueueDeclareOptions::default();
    options.durable = true;

    channel
        .queue_declare("reforged_bus_queue", options, args)
        .await?;

    channel
        .queue_bind(
            "reforged_bus_queue",
            &broker_config.exchange,
            "",
            QueueBindOptions::default(),
            FieldTable::default(),
        )
        .await?;

    let rabbit_mq = RabbitMQ::new(broker_config, connection, channel);
    let rabbit_mq = Arc::new(rabbit_mq);

    let captcha_service = TurnstileCaptcha::new(captcha_config.secret_key());
    let captcha_service = Arc::new(captcha_service);

    let user_query_handler = UserQueryHandler::builder()
        .user_repo(user_read_repository)
        .build();
    let class_category_query_handler =
        ClassCategoryQueryHandler::new(class_category_read_repository.clone());
    let password_reset_token_query_handler =
        PasswordResetTokenQueryHandler::new(password_reset_token_read_repository);
    let skill_query_handler = SkillQueryHandler::new(skill_read_repository);
    let item_rarity_query_handler = ItemRarityQueryHandler::new(item_rarity_read_repository);

    let token_service = PasetoAuthenticationTokenService::new(token_config.symmetric_key)
        .map_err(|e| anyhow::anyhow!("Failed to create token service: {:?}", e))?;
    let token_service = Arc::new(token_service);

    // Event stores
    let user_event_bus = UserAggregateEventBus::new(rabbit_mq.clone());
    let user_store = PgStoreBuilder::new(eventstore_pool.get_postgres_connection_pool().clone())
        .add_event_bus(user_event_bus)
        .try_build()
        .await?;

    let user_store_manager = AggregateManager::new(user_store);
    let user_store_manager = Arc::new(user_store_manager);

    let storage_provider = DiskStorageProvider::default();
    storage_provider.setup().await;

    let storage_provider = Arc::new(storage_provider);

    let api_state = ApiState::builder()
        .user_repository(user_repository)
        .class_category_repository(class_category_repository)
        .password_reset_token_repository(password_reset_token_repository)
        .skill_repository(skill_repository)
        .user_query_handler(user_query_handler)
        .class_category_query_handler(class_category_query_handler)
        .password_reset_token_query_handler(password_reset_token_query_handler)
        .skill_query_handler(skill_query_handler)
        .password_hasher(password_hasher)
        .token_service(token_service)
        .captcha_service(captcha_service)
        .broker_service(rabbit_mq)
        .allowed_origins(http_config.cors_allowed_origins())
        .user_store_manager(user_store_manager)
        .storage_provider(storage_provider)
        .item_rarity_query_handler(item_rarity_query_handler)
        .build();

    let http_addr = http_config.get_connection_string();
    let http_addr_clone = http_config.get_connection_string();

    tokio::spawn(async move {
        let http_addr = http_addr_clone.clone();
        info!("listening on {}", http_addr);
    });

    let server = HttpServer::new(move || {
        let api_state = api_state.clone();
        let router = create_router(api_state);
        router
    })
    .workers(2)
    .bind(http_addr)?
    .shutdown_timeout(5)
    .run();

    let server_handle = server.handle();

    tokio::spawn(async move {
        shutdown_signal(server_handle).await;
    });

    server.await?;

    Ok(())
}

pub fn setup_tracing() {
    let crate_name = env!("CARGO_CRATE_NAME");
    let crate_version = env!("CARGO_PKG_VERSION");

    let filter_layer = tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        format!("RUST_LOG=info,{}=info,reforged_api=info,reforged_infrastructure=info,reforged_application=info,tokio=trace,runtime=trace,actix_web=info", crate_name).into()
    });

    let fmt_layer = tracing_subscriber::fmt::layer().with_filter(filter_layer);
    let subscriber = tracing_subscriber::registry().with(fmt_layer);

    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global default subscriber");

    LogTracer::init().expect("Failed to set logger");

    info!("[REFORGED] {} v{}", crate_name, crate_version);
}

pub async fn shutdown_signal(handle: ServerHandle) {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to initialize Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to initialize signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("gracefully shutting down server...");
    info!("closing database pool connections...");
    info!("successfully closed database pool connections");
    info!("shutting down server...");
    info!("server shutdown complete");
    info!("goodbye!");

    handle.stop(true).await;
}

pub fn setup_rustls() {
    rustls::crypto::ring::default_provider()
        .install_default()
        .expect("Failed to install rustls crypto provider");
}
