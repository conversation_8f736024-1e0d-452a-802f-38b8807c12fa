use std::sync::Arc;

use reforged_domain::{
    models::skill::{
        entity::Skill,
        value_object::{
            Animation, Chance, Cooldown, Damage, Dsrc, Effects, HitTargets, Icon, LifeSteal, Mana,
            ManaBack, Pet, Range, Reference, ShowDamage, SkillDescription, SkillId, SkillName,
            SkillType, Strl, Target,
        },
    },
    repository::skill_repository::SkillRepository,
};
use uuid::NoContext;

use crate::{
    error::ApplicationError,
    queries::skills::{handler::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>, queries::GetSkillByNameQuery},
    traits::Query<PERSON>and<PERSON>,
};

pub struct CreateSkillUsecase {
    skill_query_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    skill_repository: Arc<dyn SkillRepository>,
}

impl CreateSkillUsecase {
    pub fn new(
        skill_query_handler: SkillQueryHandler,
        skill_repository: Arc<dyn SkillRepository>,
    ) -> Self {
        Self {
            skill_query_handler,
            skill_repository,
        }
    }
}

impl CreateSkillUsecase {
    #[allow(clippy::too_many_arguments)]
    pub async fn execute(
        &self,
        name: String,
        animation: String,
        description: String,
        damage: f64,
        mana: i16,
        mana_back: i16,
        life_steal: f64,
        icon: String,
        range: i32,
        dsrc: String,
        reference: String,
        target: String,
        effects: String,
        skill_type: String,
        strl: String,
        cooldown: i32,
        hit_targets: i16,
        pet: bool,
        chance: Option<f64>,
        show_damage: bool,
    ) -> Result<Skill, ApplicationError> {
        let query = GetSkillByNameQuery { name: name.clone() };
        let skill = self.skill_query_handler.handle(query).await;
        if skill.is_ok() && skill.as_ref().unwrap().is_some() {
            return Err(ApplicationError::EntityAlreadyExists(name.clone()));
        }

        let new_skill = Skill::new(
            SkillId::new(uuid::Uuid::new_v7(uuid::Timestamp::from_unix(
                NoContext, 1, 0,
            ))),
            SkillName::new(name),
            Animation::new(animation),
            SkillDescription::new(description),
            Damage::new(damage),
            Mana::new(mana),
            ManaBack::new(mana_back),
            LifeSteal::new(life_steal),
            Icon::new(icon),
            Range::new(range),
            Dsrc::new(dsrc),
            Reference::new(reference),
            Target::new(target),
            Effects::new(effects),
            SkillType::new(skill_type),
            Strl::new(strl),
            Cooldown::new(cooldown),
            HitTargets::new(hit_targets),
            Pet::new(pet),
            Chance::new(chance),
            ShowDamage::new(show_damage),
        );

        let skill = self.skill_repository.save(&new_skill).await?;

        Ok(skill)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use mockall::predicate::*;
    use reforged_domain::repository::skill_repository::{MockSkillReadRepository, MockSkillRepository};
    use reforged_shared::Value;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_create_skill_success() {
        // Arrange
        let mut mock_read_repo = MockSkillReadRepository::new();
        let mut mock_write_repo = MockSkillRepository::new();

        // Mock that skill doesn't exist
        mock_read_repo
            .expect_find_by_name()
            .with(eq("Test Skill"))
            .times(1)
            .returning(|_| Ok(None));

        // Mock successful save
        mock_write_repo
            .expect_save()
            .times(1)
            .returning(|skill| Ok(skill.clone()));

        let skill_query_handler = SkillQueryHandler::new(Arc::new(mock_read_repo));
        let usecase = CreateSkillUsecase::new(skill_query_handler, Arc::new(mock_write_repo));

        // Act
        let result = usecase
            .execute(
                "Test Skill".to_string(),
                "test_animation".to_string(),
                "Test Description".to_string(),
                100.0,
                50,
                10,
                5.0,
                "test_icon.png".to_string(),
                10,
                "test_dsrc".to_string(),
                "test_reference".to_string(),
                "test_target".to_string(),
                "test_effects".to_string(),
                "test_type".to_string(),
                "test_strl".to_string(),
                30,
                3,
                false,
                Some(75.0),
                true,
            )
            .await;

        // Assert
        assert!(result.is_ok());
        let skill = result.unwrap();
        assert_eq!(skill.name().value(), "Test Skill");
        assert_eq!(skill.damage().value(), 100.0);
        assert_eq!(skill.mana().value(), 50);
    }

    #[tokio::test]
    async fn test_create_skill_already_exists() {
        // Arrange
        let mut mock_read_repo = MockSkillReadRepository::new();
        let mock_write_repo = MockSkillRepository::new();

        // Mock that skill already exists
        mock_read_repo
            .expect_find_by_name()
            .with(eq("Existing Skill"))
            .times(1)
            .returning(|_| {
                Ok(Some(Skill::new(
                    SkillId::new(uuid::Uuid::now_v7()),
                    SkillName::new("Existing Skill"),
                    Animation::new("animation"),
                    SkillDescription::new("description"),
                    Damage::new(50.0),
                    Mana::new(25),
                    ManaBack::new(5),
                    LifeSteal::new(2.5),
                    Icon::new("icon.png"),
                    Range::new(5),
                    Dsrc::new("dsrc"),
                    Reference::new("reference"),
                    Target::new("target"),
                    Effects::new("effects"),
                    SkillType::new("type"),
                    Strl::new("strl"),
                    Cooldown::new(15),
                    HitTargets::new(1),
                    Pet::new(false),
                    Chance::new(None),
                    ShowDamage::new(true),
                )))
            });

        let skill_query_handler = SkillQueryHandler::new(Arc::new(mock_read_repo));
        let usecase = CreateSkillUsecase::new(skill_query_handler, Arc::new(mock_write_repo));

        // Act
        let result = usecase
            .execute(
                "Existing Skill".to_string(),
                "test_animation".to_string(),
                "Test Description".to_string(),
                100.0,
                50,
                10,
                5.0,
                "test_icon.png".to_string(),
                10,
                "test_dsrc".to_string(),
                "test_reference".to_string(),
                "test_target".to_string(),
                "test_effects".to_string(),
                "test_type".to_string(),
                "test_strl".to_string(),
                30,
                3,
                false,
                Some(75.0),
                true,
            )
            .await;

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::EntityAlreadyExists(name) => {
                assert_eq!(name, "Existing Skill");
            }
            _ => panic!("Expected EntityAlreadyExists error"),
        }
    }
}