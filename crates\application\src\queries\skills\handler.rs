use std::sync::Arc;

use async_trait::async_trait;
use reforged_domain::{
    models::skill::entity::Skill, repository::skill_repository::SkillReadRepository,
};
use reforged_shared::{IdTrait, Value, uuid_generator::uuid_to_u64};
use serde::Serialize;

use crate::traits::QueryHandler;

use super::queries::{GetSkillByIdQuery, ListAllSkillsQuery};

#[derive(Clone, bon::Builder)]
pub struct SkillQueryHandler {
    skill_repo: Arc<dyn SkillReadRepository>,
}

impl SkillQueryHandler {
    pub fn new(skill_repo: Arc<dyn SkillReadRepository>) -> Self {
        Self { skill_repo }
    }
}

#[async_trait]
impl QueryHandler<ListAllSkillsQuery> for SkillQueryHandler {
    type Output = Vec<SkillResponse>;

    async fn handle(
        &self,
        _query: ListAllSkillsQuery,
    ) -> Result<Vec<SkillResponse>, crate::error::ApplicationError> {
        let skill_repo = self.skill_repo.clone();
        let skills = skill_repo
            .find_all()
            .await?
            .into_iter()
            .map(SkillResponse::from)
            .collect();

        Ok(skills)
    }
}

#[async_trait]
impl QueryHandler<GetSkillByIdQuery> for SkillQueryHandler {
    type Output = SkillResponse;

    async fn handle(
        &self,
        query: GetSkillByIdQuery,
    ) -> Result<SkillResponse, crate::error::ApplicationError> {
        let skill_repo = self.skill_repo.clone();
        let skill = skill_repo.find_by_id(&query.id).await?.ok_or(
            crate::error::ApplicationError::EntityNotFound(format!(
                "Skill with ID {} not found",
                query.id
            )),
        )?;

        Ok(SkillResponse::from(skill))
    }
}

#[derive(Serialize)]
pub struct SkillResponse {
    pub id: uuid::Uuid,
    pub pid: u64,
    pub name: String,
    pub animation: String,
    pub description: String,
    pub damage: f64,
    pub mana: i16,
    pub mana_back: i16,
    pub life_steal: f64,
    pub icon: String,
    pub range: i32,
    pub dsrc: String,
    pub reference: String,
    pub target: String,
    pub effects: String,
    pub skill_type: String,
    pub strl: String,
    pub cooldown: i32,
    pub hit_targets: i16,
    pub pet: bool,
    pub chance: Option<f64>,
    pub show_damage: bool,
}

impl From<Skill> for SkillResponse {
    fn from(value: Skill) -> Self {
        let pid = uuid_to_u64(&value.id().get_id());

        Self {
            id: value.id().get_id(),
            pid,
            name: value.name().value().to_string(),
            animation: value.animation().value().to_string(),
            description: value.description().value().to_string(),
            damage: value.damage().value(),
            mana: value.mana().value(),
            mana_back: value.mana_back().value(),
            life_steal: value.life_steal().value(),
            icon: value.icon().value().to_string(),
            range: value.range().value(),
            dsrc: value.dsrc().value().to_string(),
            reference: value.reference().value().to_string(),
            target: value.target().value().to_string(),
            effects: value.effects().value().to_string(),
            skill_type: value.skill_type().value().to_string(),
            strl: value.strl().value().to_string(),
            cooldown: value.cooldown().value(),
            hit_targets: value.hit_targets().value(),
            pet: value.pet().value(),
            chance: value.chance().value(),
            show_damage: value.show_damage().value(),
        }
    }
}
