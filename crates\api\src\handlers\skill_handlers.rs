use actix_web::{
    <PERSON>ope,
    web::{self, <PERSON>, <PERSON><PERSON>},
};
use reforged_application::{
    queries::skills::handler::SkillResponse,
    usecases::skill::{
        create_skill_usecase::{CreateSkillRequest, CreateSkillUsecase},
        get_all_skills_usecase::GetAllSkillsUsecase,
        get_skill_by_id_usecase::GetSkillByIdUsecase,
    },
};
use uuid::Uuid;

use crate::{
    dtos::{skill_dtos::CreateSkillDTO, validate::ValidateJson},
    error::ApiError,
    state::ApiState,
};

pub fn create_skill_service() -> Scope {
    web::scope("/skills")
        .service(
            web::resource("")
                .route(web::get().to(get_all_skills_handler))
                .route(web::post().to(create_skill_handler)),
        )
        .service(web::resource("{id}").route(web::get().to(get_skill_by_id)))
}

async fn get_all_skills_handler(
    state: Data<ApiState>,
) -> Result<Json<Vec<SkillResponse>>, ApiError> {
    let skill_query_handler = state.skill_query_handler().clone();
    let usecase = GetAllSkillsUsecase::new(skill_query_handler);

    let skills = usecase.execute().await?;

    Ok(Json(skills))
}

async fn get_skill_by_id(
    state: Data<ApiState>,
    id: web::Path<Uuid>,
) -> Result<Json<SkillResponse>, ApiError> {
    let skill_query_handler = state.skill_query_handler().clone();
    let usecase = GetSkillByIdUsecase::new(skill_query_handler);

    let skill = usecase.execute(id.into_inner()).await?;

    Ok(Json(skill))
}

async fn create_skill_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<CreateSkillDTO>,
) -> Result<Json<SkillResponse>, ApiError> {
    let usecase = CreateSkillUsecase::new(
        state.skill_query_handler().clone(),
        state.skill_repository().clone(),
    );

    let request = CreateSkillRequest {
        name: dto.name,
        animation: dto.animation,
        description: dto.description,
        damage: dto.damage,
        mana: dto.mana,
        mana_back: dto.mana_back,
        life_steal: dto.life_steal,
        icon: dto.icon,
        range: dto.range,
        dsrc: dto.dsrc,
        reference: dto.reference,
        target: dto.target,
        effects: dto.effects,
        skill_type: dto.skill_type,
        strl: dto.strl,
        cooldown: dto.cooldown,
        hit_targets: dto.hit_targets,
        pet: dto.pet,
        chance: dto.chance,
        show_damage: dto.show_damage,
    };

    let skill = usecase.execute(request).await?;

    Ok(Json(skill))
}
