use std::sync::Arc;

use getset::Get<PERSON>;
use reforged_application::{
    eventsource::user::UserStoreManager,
    queries::{
        class_category::handler::ClassCategoryQuery<PERSON><PERSON><PERSON>,
        item_rarities::handler::ItemRarityQuery<PERSON>and<PERSON>,
        password_reset_token::handler::PasswordResetTokenQ<PERSON>y<PERSON>andler,
        skills::handler::<PERSON><PERSON><PERSON>ueryH<PERSON><PERSON>, user::handler::UserQueryHandler,
    },
    services::captcha_service::Captcha,
    traits::{BrokerServicePublisher, TokenService},
};
use reforged_domain::{
    repository::{
        class_category_repository::ClassCategoryRepository,
        password_reset_token_repository::PasswordResetTokenRepository,
        skill_repository::SkillRepository,
        user_repository::UserRepository,
    },
    traits::{password_hasher::PasswordHasher, storage_provider::StorageProvider},
};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, bon::Builder)]
#[getset(get = "pub")]
pub struct ApiState {
    user_query_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    class_category_query_handler: ClassCategory<PERSON><PERSON>yHandler,
    password_reset_token_query_handler: PasswordResetTokenQueryHandler,
    skill_query_handler: SkillQueryHandler,
    item_rarity_query_handler: ItemRarityQueryHandler,
    captcha_service: Arc<dyn Captcha>,
    password_hasher: Arc<dyn PasswordHasher>,
    token_service: Arc<dyn TokenService>,
    user_repository: Arc<dyn UserRepository>,
    class_category_repository: Arc<dyn ClassCategoryRepository>,
    password_reset_token_repository: Arc<dyn PasswordResetTokenRepository>,
    skill_repository: Arc<dyn SkillRepository>,
    allowed_origins: Vec<String>,
    broker_service: Arc<dyn BrokerServicePublisher>,
    user_store_manager: Arc<UserStoreManager>,
    storage_provider: Arc<dyn StorageProvider>,
}
