use std::sync::Arc;

use reforged_domain::{
    models::skill::value_object::SkillId,
    repository::skill_repository::SkillRepository,
};

use crate::error::ApplicationError;

pub struct DeleteSkillUsecase {
    skill_repository: Arc<dyn SkillRepository>,
}

impl DeleteSkillUsecase {
    pub fn new(skill_repository: Arc<dyn SkillRepository>) -> Self {
        Self {
            skill_repository,
        }
    }
}

impl DeleteSkillUsecase {
    pub async fn execute(&self, id: uuid::Uuid) -> Result<(), ApplicationError> {
        let id = SkillId::new(id);
        self.skill_repository.delete(&id).await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use mockall::predicate::*;
    use reforged_domain::{
        error::RepositoryError,
        repository::skill_repository::MockSkillRepository,
    };
    use std::sync::Arc;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_delete_skill_success() {
        // Arrange
        let mut mock_repo = MockSkillRepository::new();
        let skill_id = Uuid::new_v4();

        // Mock successful deletion
        mock_repo
            .expect_delete()
            .with(eq(SkillId::new(skill_id)))
            .times(1)
            .returning(|_| Ok(()));

        let usecase = DeleteSkillUsecase::new(Arc::new(mock_repo));

        // Act
        let result = usecase.execute(skill_id).await;

        // Assert
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_delete_skill_not_found() {
        // Arrange
        let mut mock_repo = MockSkillRepository::new();
        let skill_id = Uuid::new_v4();

        // Mock skill not found error
        mock_repo
            .expect_delete()
            .with(eq(SkillId::new(skill_id)))
            .times(1)
            .returning(|id| Err(RepositoryError::NotFound(format!("Skill with id {}", id.get_id()))));

        let usecase = DeleteSkillUsecase::new(Arc::new(mock_repo));

        // Act
        let result = usecase.execute(skill_id).await;

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::RepositoryError(RepositoryError::NotFound(msg)) => {
                assert!(msg.contains(&skill_id.to_string()));
            }
            _ => panic!("Expected RepositoryError::NotFound"),
        }
    }

    #[tokio::test]
    async fn test_delete_skill_repository_error() {
        // Arrange
        let mut mock_repo = MockSkillRepository::new();
        let skill_id = Uuid::new_v4();

        // Mock database error
        mock_repo
            .expect_delete()
            .with(eq(SkillId::new(skill_id)))
            .times(1)
            .returning(|_| Err(RepositoryError::DatabaseError("Connection failed".to_string())));

        let usecase = DeleteSkillUsecase::new(Arc::new(mock_repo));

        // Act
        let result = usecase.execute(skill_id).await;

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::RepositoryError(RepositoryError::DatabaseError(msg)) => {
                assert_eq!(msg, "Connection failed");
            }
            _ => panic!("Expected RepositoryError::DatabaseError"),
        }
    }
}